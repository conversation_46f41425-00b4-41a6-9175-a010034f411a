# Poseidon Rental Pro - Apache Configuration

# Enable URL Rewriting
RewriteEngine On

# Hide sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Protect configuration and log files
<FilesMatch "\.(ini|log|conf|sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Protect backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Enable compression (optional)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>

# Custom error pages (optional)
# ErrorDocument 404 /pages/404.php
# ErrorDocument 500 /pages/500.php
