# Poseidon Rental Pro - Deployment Guide

## 🚀 Quick Start (Local Development)

### Prerequisites
- PHP 7.4+ with MySQL extension
- MySQL 5.7+ or MariaDB
- Web server (Apache/Nginx) or PHP built-in server

### 1. Database Setup
```sql
-- Create database
CREATE DATABASE poseidon_rental;

-- Import schema
mysql -u root -p poseidon_rental < database/schema.sql
```

### 2. Configuration
Update `config/database.php` with your database credentials:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'poseidon_rental');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. Start Development Server
```bash
# Windows
start.bat

# Linux/Mac
php -S localhost:8000
```

### 4. Access the Platform
- **Frontend:** http://localhost:8000
- **Admin Panel:** http://localhost:8000/admin/
- **Default Admin:** <EMAIL> / admin123

## 🌐 Production Deployment

### 1. Server Requirements
- **PHP:** 7.4+ with extensions: PDO, MySQL, OpenSSL, JSON
- **Database:** MySQL 5.7+ or MariaDB 10.2+
- **Web Server:** Apache 2.4+ or Nginx 1.18+
- **SSL Certificate:** Required for production
- **Cron Jobs:** For automated profit calculations

### 2. File Permissions
```bash
# Set proper permissions
chmod 755 logs/
chmod 755 assets/images/
chmod 644 .htaccess
chmod 600 config/*.php
```

### 3. Environment Configuration

#### Apache Virtual Host
```apache
<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /path/to/poseidon
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    <Directory /path/to/poseidon>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Security headers
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
</VirtualHost>
```

#### Nginx Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    root /path/to/poseidon;
    index index.php;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(ini|log|conf|sql)$ {
        deny all;
    }
}
```

### 4. Database Optimization
```sql
-- Add indexes for better performance
ALTER TABLE user_investments ADD INDEX idx_user_active (user_id, is_active);
ALTER TABLE daily_profits ADD INDEX idx_date (profit_date);
ALTER TABLE transactions ADD INDEX idx_status_type (status, type);

-- Optimize tables
OPTIMIZE TABLE users, investments, user_investments, transactions, daily_profits;
```

### 5. Cron Job Setup
```bash
# Edit crontab
crontab -e

# Add daily profit calculation (runs at midnight)
0 0 * * * /usr/bin/php /path/to/poseidon/cron/daily_profit.php

# Optional: Database backup (runs at 2 AM)
0 2 * * * mysqldump -u username -p password poseidon_rental > /backups/poseidon_$(date +\%Y\%m\%d).sql
```

### 6. Security Hardening

#### PHP Configuration (php.ini)
```ini
; Hide PHP version
expose_php = Off

; Session security
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1

; File upload limits
upload_max_filesize = 5M
post_max_size = 10M
max_execution_time = 30
memory_limit = 128M

; Disable dangerous functions
disable_functions = exec,passthru,shell_exec,system,proc_open,popen
```

#### File System Security
```bash
# Create dedicated user for the application
useradd -r -s /bin/false poseidon

# Set ownership
chown -R poseidon:poseidon /path/to/poseidon

# Secure sensitive directories
chmod 700 config/
chmod 700 logs/
chmod 700 database/
```

### 7. Monitoring & Logging

#### Log Rotation
```bash
# Create logrotate configuration
cat > /etc/logrotate.d/poseidon << EOF
/path/to/poseidon/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

#### Health Check Script
```bash
#!/bin/bash
# health_check.sh

# Check database connection
php -r "
require_once '/path/to/poseidon/config/database.php';
try {
    \$db = new Database();
    \$conn = \$db->getConnection();
    echo 'Database: OK\n';
} catch (Exception \$e) {
    echo 'Database: FAILED - ' . \$e->getMessage() . '\n';
    exit 1;
}
"

# Check web server response
curl -f -s http://localhost/health > /dev/null
if [ $? -eq 0 ]; then
    echo "Web server: OK"
else
    echo "Web server: FAILED"
    exit 1
fi
```

## 🔧 Configuration Options

### Environment Variables
Create `.env` file for sensitive configuration:
```env
DB_HOST=localhost
DB_NAME=poseidon_rental
DB_USER=your_username
DB_PASS=your_password

SITE_URL=https://yourdomain.com
ADMIN_EMAIL=<EMAIL>

SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### Performance Tuning
```php
// config/config.php additions for production

// Enable OPcache
if (function_exists('opcache_get_status')) {
    ini_set('opcache.enable', 1);
    ini_set('opcache.memory_consumption', 128);
    ini_set('opcache.max_accelerated_files', 4000);
}

// Database connection pooling
define('DB_PERSISTENT', true);

// Cache settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600);
```

## 📊 Monitoring & Analytics

### Key Metrics to Monitor
- **User registrations** per day/week/month
- **Total investments** and amounts
- **Daily profit distributions**
- **Pending transactions** count
- **System performance** (response times, errors)
- **Database performance** (slow queries, connections)

### Recommended Tools
- **Uptime monitoring:** UptimeRobot, Pingdom
- **Error tracking:** Sentry, Rollbar
- **Performance:** New Relic, DataDog
- **Analytics:** Google Analytics, Matomo

## 🔄 Backup Strategy

### Database Backups
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/poseidon"
DB_NAME="poseidon_rental"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u username -p password $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: db_$DATE.sql.gz"
```

### File Backups
```bash
# Backup uploaded files and logs
tar -czf /backups/poseidon/files_$(date +%Y%m%d).tar.gz \
    /path/to/poseidon/assets/images/ \
    /path/to/poseidon/logs/
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check credentials in `config/database.php`
   - Verify MySQL service is running
   - Check firewall settings

2. **Permission Denied Errors**
   - Set proper file permissions: `chmod 755 logs/ assets/`
   - Check web server user ownership

3. **Cron Jobs Not Running**
   - Verify crontab: `crontab -l`
   - Check PHP path: `which php`
   - Review cron logs: `/var/log/cron`

4. **SSL Certificate Issues**
   - Verify certificate validity
   - Check intermediate certificates
   - Test with SSL checker tools

### Debug Mode
Enable debug mode for development:
```php
// config/config.php
define('DEBUG_MODE', true);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
}
```

## 📞 Support

For deployment assistance:
- **Documentation:** This guide and README.md
- **Issues:** Create GitHub issues
- **Email:** <EMAIL>

---

**Remember:** Always test thoroughly in a staging environment before deploying to production!
