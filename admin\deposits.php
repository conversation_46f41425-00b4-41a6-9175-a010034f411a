<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

requireAdmin();

$page_title = 'Manage Deposits';

// Handle deposit approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', 'Invalid security token');
    } else {
        $transaction_id = intval($_POST['transaction_id'] ?? 0);
        $action = $_POST['action'];
        $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');
        
        if ($transaction_id && in_array($action, ['approve', 'reject'])) {
            $status = $action === 'approve' ? 'approved' : 'rejected';
            
            $db = getDB();
            
            try {
                $db->beginTransaction();
                
                // Get transaction details
                $stmt = $db->prepare("SELECT * FROM transactions WHERE id = ? AND type = 'deposit'");
                $stmt->execute([$transaction_id]);
                $transaction = $stmt->fetch();
                
                if ($transaction) {
                    // Update transaction status
                    updateTransactionStatus($transaction_id, $status, $admin_notes);
                    
                    // If approved, add funds to user balance
                    if ($status === 'approved') {
                        updateUserBalance($transaction['user_id'], $transaction['amount'], 'add');
                    }
                    
                    $db->commit();
                    setFlashMessage('success', "Deposit {$status} successfully");
                } else {
                    $db->rollback();
                    setFlashMessage('error', 'Transaction not found');
                }
                
            } catch (Exception $e) {
                $db->rollback();
                setFlashMessage('error', 'Error processing transaction: ' . $e->getMessage());
            }
        }
    }
    
    redirect('deposits.php');
}

// Get pending deposits
$pending_deposits = getPendingTransactions('deposit');

// Get recent processed deposits
$db = getDB();
$stmt = $db->prepare("
    SELECT t.*, u.username, u.email 
    FROM transactions t 
    JOIN users u ON t.user_id = u.id 
    WHERE t.type = 'deposit' AND t.status != 'pending'
    ORDER BY t.processed_at DESC 
    LIMIT 20
");
$stmt->execute();
$processed_deposits = $stmt->fetchAll();

include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-primary">Manage Deposits</h1>
                <p class="text-gray-600 mt-2">Review and process user deposit requests</p>
            </div>
            <a href="index.php" class="btn-primary text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
            </a>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Pending Deposits</p>
                        <p class="text-2xl font-bold text-primary"><?php echo count($pending_deposits); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Pending Amount</p>
                        <p class="text-2xl font-bold text-primary">
                            <?php 
                            $total_pending = array_sum(array_column($pending_deposits, 'amount'));
                            echo formatCurrency($total_pending); 
                            ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-history text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Processed Today</p>
                        <p class="text-2xl font-bold text-primary">
                            <?php 
                            $today_processed = array_filter($processed_deposits, function($d) {
                                return date('Y-m-d', strtotime($d['processed_at'])) === date('Y-m-d');
                            });
                            echo count($today_processed);
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Deposits -->
        <div class="bg-white rounded-xl card-shadow mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-primary">Pending Deposits</h2>
            </div>
            <div class="p-6">
                <?php if (empty($pending_deposits)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-check-circle text-green-500 text-4xl mb-4"></i>
                        <p class="text-gray-600">No pending deposits to review</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">User</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Amount</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Transaction Hash</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                                    <th class="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_deposits as $deposit): ?>
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="font-semibold text-gray-800"><?php echo htmlspecialchars($deposit['username']); ?></p>
                                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($deposit['email']); ?></p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-lg font-bold text-green-600"><?php echo formatCurrency($deposit['amount']); ?></span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="max-w-xs">
                                                <p class="text-sm text-gray-800 break-all"><?php echo htmlspecialchars($deposit['transaction_hash']); ?></p>
                                                <button onclick="copyToClipboard('<?php echo htmlspecialchars($deposit['transaction_hash']); ?>')" 
                                                        class="text-xs text-blue-600 hover:text-blue-800 mt-1">
                                                    <i class="fas fa-copy mr-1"></i>Copy
                                                </button>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-gray-600"><?php echo timeAgo($deposit['created_at']); ?></span>
                                        </td>
                                        <td class="py-4 px-4 text-center">
                                            <div class="flex justify-center space-x-2">
                                                <button onclick="openApprovalModal(<?php echo $deposit['id']; ?>, 'approve', '<?php echo htmlspecialchars($deposit['username']); ?>', '<?php echo formatCurrency($deposit['amount']); ?>')" 
                                                        class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                                                    <i class="fas fa-check mr-1"></i>Approve
                                                </button>
                                                <button onclick="openApprovalModal(<?php echo $deposit['id']; ?>, 'reject', '<?php echo htmlspecialchars($deposit['username']); ?>', '<?php echo formatCurrency($deposit['amount']); ?>')" 
                                                        class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                                                    <i class="fas fa-times mr-1"></i>Reject
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Processed Deposits -->
        <div class="bg-white rounded-xl card-shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-primary">Recently Processed Deposits</h2>
            </div>
            <div class="p-6">
                <?php if (empty($processed_deposits)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-600">No processed deposits yet</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">User</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Amount</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Processed</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($processed_deposits as $deposit): ?>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="font-semibold text-gray-800"><?php echo htmlspecialchars($deposit['username']); ?></p>
                                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($deposit['email']); ?></p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-lg font-bold <?php echo $deposit['status'] === 'approved' ? 'text-green-600' : 'text-red-600'; ?>">
                                                <?php echo formatCurrency($deposit['amount']); ?>
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="px-2 py-1 rounded-full text-xs font-semibold <?php 
                                                echo $deposit['status'] === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; 
                                            ?>">
                                                <?php echo ucfirst($deposit['status']); ?>
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-gray-600"><?php echo timeAgo($deposit['processed_at']); ?></span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-gray-600"><?php echo htmlspecialchars($deposit['admin_notes'] ?: 'No notes'); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div id="approvalModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-primary" id="modalTitle">Approve Deposit</h3>
                <button onclick="closeApprovalModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div id="modalContent" class="mb-6">
                <!-- Content will be populated by JavaScript -->
            </div>
            
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="transaction_id" id="modal_transaction_id">
                <input type="hidden" name="action" id="modal_action">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Admin Notes</label>
                    <textarea name="admin_notes" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-blue"
                              placeholder="Optional notes about this decision..."></textarea>
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" onclick="closeApprovalModal()" 
                            class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" id="modalSubmitBtn"
                            class="flex-1 py-2 rounded-lg font-semibold text-white">
                        Confirm
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openApprovalModal(transactionId, action, username, amount) {
    document.getElementById('modal_transaction_id').value = transactionId;
    document.getElementById('modal_action').value = action;
    
    const title = action === 'approve' ? 'Approve Deposit' : 'Reject Deposit';
    const actionText = action === 'approve' ? 'approve' : 'reject';
    const buttonClass = action === 'approve' ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600';
    
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalContent').innerHTML = `
        <div class="text-center">
            <p class="text-gray-700 mb-2">Are you sure you want to ${actionText} this deposit?</p>
            <p class="font-semibold">User: ${username}</p>
            <p class="font-semibold">Amount: ${amount}</p>
        </div>
    `;
    
    const submitBtn = document.getElementById('modalSubmitBtn');
    submitBtn.className = `flex-1 py-2 rounded-lg font-semibold text-white ${buttonClass}`;
    submitBtn.textContent = `${action === 'approve' ? 'Approve' : 'Reject'} Deposit`;
    
    document.getElementById('approvalModal').classList.remove('hidden');
}

function closeApprovalModal() {
    document.getElementById('approvalModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('approvalModal').addEventListener('click', function(e) {
    if (e.target === this) closeApprovalModal();
});
</script>

<?php include '../includes/footer.php'; ?>
