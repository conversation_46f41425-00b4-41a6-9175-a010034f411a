<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

requireAdmin();

$page_title = 'Admin Dashboard';

// Get statistics
$db = getDB();

// Total users
$stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE is_admin = 0");
$stmt->execute();
$total_users = $stmt->fetch()['count'];

// Total investments
$stmt = $db->prepare("SELECT COUNT(*) as count FROM investments WHERE is_active = 1");
$stmt->execute();
$total_investments = $stmt->fetch()['count'];

// Total invested amount
$stmt = $db->prepare("SELECT SUM(amount) as total FROM user_investments WHERE is_active = 1");
$stmt->execute();
$total_invested = $stmt->fetch()['total'] ?? 0;

// Pending transactions
$stmt = $db->prepare("SELECT COUNT(*) as count FROM transactions WHERE status = 'pending'");
$stmt->execute();
$pending_transactions = $stmt->fetch()['count'];

// Recent activities
$stmt = $db->prepare("
    SELECT 'investment' as type, ui.created_at, u.username, i.title, ui.amount
    FROM user_investments ui
    JOIN users u ON ui.user_id = u.id
    JOIN investments i ON ui.investment_id = i.id
    ORDER BY ui.created_at DESC
    LIMIT 10
");
$stmt->execute();
$recent_investments = $stmt->fetchAll();

$stmt = $db->prepare("
    SELECT 'transaction' as type, t.created_at, u.username, t.type, t.amount, t.status
    FROM transactions t
    JOIN users u ON t.user_id = u.id
    ORDER BY t.created_at DESC
    LIMIT 10
");
$stmt->execute();
$recent_transactions = $stmt->fetchAll();

// Merge and sort recent activities
$recent_activities = array_merge($recent_investments, $recent_transactions);
usort($recent_activities, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});
$recent_activities = array_slice($recent_activities, 0, 10);

include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-primary">Admin Dashboard</h1>
            <p class="text-gray-600 mt-2">Manage your investment platform</p>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <a href="users.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <i class="fas fa-users text-blue-500 text-2xl mr-3"></i>
                    <div>
                        <p class="text-sm text-gray-600">Manage</p>
                        <p class="font-semibold">Users</p>
                    </div>
                </div>
            </a>
            
            <a href="investments.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-green-500 text-2xl mr-3"></i>
                    <div>
                        <p class="text-sm text-gray-600">Manage</p>
                        <p class="font-semibold">Investments</p>
                    </div>
                </div>
            </a>
            
            <a href="deposits.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <i class="fas fa-arrow-down text-purple-500 text-2xl mr-3"></i>
                    <div>
                        <p class="text-sm text-gray-600">Process</p>
                        <p class="font-semibold">Deposits</p>
                    </div>
                </div>
            </a>
            
            <a href="withdrawals.php" class="bg-white p-4 rounded-lg card-shadow hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <i class="fas fa-arrow-up text-red-500 text-2xl mr-3"></i>
                    <div>
                        <p class="text-sm text-gray-600">Process</p>
                        <p class="font-semibold">Withdrawals</p>
                    </div>
                </div>
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Users</p>
                        <p class="text-2xl font-bold text-primary"><?php echo number_format($total_users); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Active Investments</p>
                        <p class="text-2xl font-bold text-primary"><?php echo number_format($total_investments); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Invested</p>
                        <p class="text-2xl font-bold text-primary"><?php echo formatCurrency($total_invested); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Pending Transactions</p>
                        <p class="text-2xl font-bold text-primary"><?php echo number_format($pending_transactions); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Activities -->
            <div class="bg-white rounded-xl card-shadow">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-primary">Recent Activities</h2>
                </div>
                <div class="p-6">
                    <?php if (empty($recent_activities)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-600">No recent activities</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                                    <div class="flex items-center space-x-3">
                                        <?php if ($activity['type'] === 'investment'): ?>
                                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-chart-line text-green-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold"><?php echo htmlspecialchars($activity['username']); ?></p>
                                                <p class="text-sm text-gray-600">Invested in <?php echo htmlspecialchars($activity['title']); ?></p>
                                                <p class="text-xs text-gray-500"><?php echo timeAgo($activity['created_at']); ?></p>
                                            </div>
                                        <?php else: ?>
                                            <div class="w-10 h-10 <?php echo $activity['type'] === 'deposit' ? 'bg-blue-100' : 'bg-red-100'; ?> rounded-full flex items-center justify-center">
                                                <i class="fas <?php echo $activity['type'] === 'deposit' ? 'fa-arrow-down text-blue-600' : 'fa-arrow-up text-red-600'; ?>"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold"><?php echo htmlspecialchars($activity['username']); ?></p>
                                                <p class="text-sm text-gray-600"><?php echo ucfirst($activity['type']); ?> request</p>
                                                <p class="text-xs text-gray-500"><?php echo timeAgo($activity['created_at']); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold"><?php echo formatCurrency($activity['amount']); ?></p>
                                        <?php if (isset($activity['status'])): ?>
                                            <span class="text-xs px-2 py-1 rounded-full <?php 
                                                echo $activity['status'] === 'approved' ? 'bg-green-100 text-green-800' : 
                                                    ($activity['status'] === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'); 
                                            ?>">
                                                <?php echo ucfirst($activity['status']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="space-y-6">
                <!-- Pending Transactions Alert -->
                <?php if ($pending_transactions > 0): ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl mr-4"></i>
                        <div>
                            <h3 class="text-lg font-bold text-yellow-800">Attention Required</h3>
                            <p class="text-yellow-700">You have <?php echo $pending_transactions; ?> pending transaction(s) that need review.</p>
                            <div class="mt-3 space-x-3">
                                <a href="deposits.php" class="text-yellow-800 hover:text-yellow-900 font-semibold">Review Deposits →</a>
                                <a href="withdrawals.php" class="text-yellow-800 hover:text-yellow-900 font-semibold">Review Withdrawals →</a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- System Status -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-bold text-primary mb-4">System Status</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Database</span>
                            <span class="flex items-center text-green-600">
                                <i class="fas fa-check-circle mr-1"></i>
                                Online
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Daily Profit Cron</span>
                            <span class="flex items-center text-green-600">
                                <i class="fas fa-check-circle mr-1"></i>
                                Active
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Last Profit Run</span>
                            <span class="text-gray-800"><?php echo date('M j, Y'); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-bold text-primary mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <button onclick="runDailyProfit()" class="w-full btn-primary text-white py-2 rounded-lg font-semibold">
                            <i class="fas fa-play mr-2"></i>Run Daily Profit
                        </button>
                        <a href="investments.php?action=add" class="block w-full text-center border-2 border-primary text-primary py-2 rounded-lg font-semibold hover:bg-primary hover:text-white transition-colors">
                            <i class="fas fa-plus mr-2"></i>Add Investment
                        </a>
                        <a href="../cron/daily_profit.php" target="_blank" class="block w-full text-center border-2 border-gray-300 text-gray-700 py-2 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                            <i class="fas fa-eye mr-2"></i>View Cron Log
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function runDailyProfit() {
    if (confirm('Are you sure you want to run the daily profit calculation manually?')) {
        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Running...';
        btn.disabled = true;
        
        // Make AJAX request to run the cron job
        fetch('../cron/daily_profit.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.text())
        .then(data => {
            alert('Daily profit calculation completed. Check the logs for details.');
            btn.innerHTML = originalText;
            btn.disabled = false;
            location.reload();
        })
        .catch(error => {
            alert('Error running daily profit calculation: ' + error);
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}
</script>

<?php include '../includes/footer.php'; ?>
