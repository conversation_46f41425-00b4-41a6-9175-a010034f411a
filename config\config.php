<?php
// Application configuration
define('SITE_NAME', 'Poseidon Rental Pro');

// Auto-detect base URL
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = dirname($script);

    // Remove trailing slash if not root
    if ($path !== '/') {
        $path = rtrim($path, '/');
    }

    return $protocol . '://' . $host . $path;
}

define('SITE_URL', getBaseUrl());
define('ADMIN_EMAIL', '<EMAIL>');

// Function to get relative path based on current directory
function getRelativePath($target = '') {
    // Get the current script path
    $currentScript = $_SERVER['SCRIPT_NAME'];
    $currentDir = dirname($currentScript);

    // Remove leading slash and count directory levels
    $currentDir = trim($currentDir, '/');
    $levels = empty($currentDir) ? 0 : substr_count($currentDir, '/') + 1;

    // If we're in a subdirectory, go back to root
    $basePath = str_repeat('../', $levels);

    // If we're already in root, don't add ../
    if ($levels === 0) {
        $basePath = '';
    }

    return $basePath . $target;
}

// Security settings
define('SESSION_LIFETIME', 3600 * 24); // 24 hours
define('CSRF_TOKEN_NAME', 'csrf_token');

// File upload settings
define('UPLOAD_PATH', 'assets/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Email settings (for password reset)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');

// Pagination
define('ITEMS_PER_PAGE', 10);

// Investment settings
define('MIN_INVESTMENT_AMOUNT', 100);
define('MAX_INVESTMENT_AMOUNT', 1000000);

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// CSRF Token generation
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// CSRF Token validation
function validateCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

// Sanitize input
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// Format currency
function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}

// Format percentage
function formatPercentage($rate) {
    return number_format($rate, 2) . '%';
}

// Time ago function
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}

// Redirect function
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Check if user is admin
function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == true;
}

// Get current user ID
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

// Flash messages
function setFlashMessage($type, $message) {
    $_SESSION['flash'][$type] = $message;
}

function getFlashMessage($type) {
    if (isset($_SESSION['flash'][$type])) {
        $message = $_SESSION['flash'][$type];
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    return null;
}

// Error logging
function logError($message) {
    error_log(date('[Y-m-d H:i:s] ') . $message . PHP_EOL, 3, 'logs/error.log');
}
?>
