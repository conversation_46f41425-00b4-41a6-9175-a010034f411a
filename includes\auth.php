<?php
require_once 'functions.php';

// Login function
function loginUser($email, $password) {
    $user = getUserByEmail($email);
    
    if ($user && password_verify($password, $user['password_hash'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['is_admin'] = $user['is_admin'];
        
        return true;
    }
    
    return false;
}

// Logout function
function logoutUser() {
    session_destroy();
    session_start();
}

// Registration function
function registerUser($username, $email, $password) {
    // Check if user already exists
    if (getUserByEmail($email)) {
        return ['success' => false, 'message' => 'Email already exists'];
    }
    
    // Validate input
    if (strlen($password) < 6) {
        return ['success' => false, 'message' => 'Password must be at least 6 characters'];
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'message' => 'Invalid email format'];
    }
    
    if (strlen($username) < 3) {
        return ['success' => false, 'message' => 'Username must be at least 3 characters'];
    }
    
    // Create user
    if (createUser($username, $email, $password)) {
        return ['success' => true, 'message' => 'Registration successful'];
    } else {
        return ['success' => false, 'message' => 'Registration failed'];
    }
}

// Password reset token generation
function generatePasswordResetToken($email) {
    $user = getUserByEmail($email);
    
    if (!$user) {
        return false;
    }
    
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    $db = getDB();
    $stmt = $db->prepare("UPDATE users SET reset_token = ?, reset_expires = ? WHERE id = ?");
    
    if ($stmt->execute([$token, $expires, $user['id']])) {
        return $token;
    }
    
    return false;
}

// Validate password reset token
function validatePasswordResetToken($token) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT * FROM users 
        WHERE reset_token = ? AND reset_expires > NOW()
    ");
    $stmt->execute([$token]);
    
    return $stmt->fetch();
}

// Reset password
function resetPassword($token, $new_password) {
    $user = validatePasswordResetToken($token);
    
    if (!$user) {
        return false;
    }
    
    $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
    
    $db = getDB();
    $stmt = $db->prepare("
        UPDATE users 
        SET password_hash = ?, reset_token = NULL, reset_expires = NULL 
        WHERE id = ?
    ");
    
    return $stmt->execute([$password_hash, $user['id']]);
}

// Require login middleware
function requireLogin() {
    if (!isLoggedIn()) {
        redirect('pages/login.php');
    }
}

// Require admin middleware
function requireAdmin() {
    if (!isLoggedIn() || !isAdmin()) {
        redirect('index.php');
    }
}

// Check investment eligibility
function canInvest($user_id, $amount) {
    $user = getUserById($user_id);
    
    if (!$user) {
        return ['success' => false, 'message' => 'User not found'];
    }
    
    if ($user['balance'] < $amount) {
        return ['success' => false, 'message' => 'Insufficient balance'];
    }
    
    if ($amount < MIN_INVESTMENT_AMOUNT) {
        return ['success' => false, 'message' => 'Minimum investment amount is $' . MIN_INVESTMENT_AMOUNT];
    }
    
    if ($amount > MAX_INVESTMENT_AMOUNT) {
        return ['success' => false, 'message' => 'Maximum investment amount is $' . MAX_INVESTMENT_AMOUNT];
    }
    
    return ['success' => true];
}

// Process investment
function processInvestment($user_id, $investment_id, $amount) {
    $eligibility = canInvest($user_id, $amount);
    
    if (!$eligibility['success']) {
        return $eligibility;
    }
    
    $investment = getInvestmentById($investment_id);
    
    if (!$investment || !$investment['is_active']) {
        return ['success' => false, 'message' => 'Investment not available'];
    }
    
    // Generate random monthly rate within the range
    $monthly_rate = rand($investment['monthly_rate_min'] * 100, $investment['monthly_rate_max'] * 100) / 100;
    
    $db = getDB();
    
    try {
        $db->beginTransaction();
        
        // Deduct amount from user balance
        updateUserBalance($user_id, $amount, 'subtract');
        
        // Create user investment
        createUserInvestment($user_id, $investment_id, $amount, $monthly_rate, $investment['return_period_months']);
        
        $db->commit();
        
        return ['success' => true, 'message' => 'Investment successful'];
        
    } catch (Exception $e) {
        $db->rollback();
        logError("Investment processing error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Investment processing failed'];
    }
}

// Process withdrawal request
function processWithdrawalRequest($user_id, $amount, $wallet_address) {
    $user = getUserById($user_id);
    
    if (!$user) {
        return ['success' => false, 'message' => 'User not found'];
    }
    
    if ($user['balance'] < $amount) {
        return ['success' => false, 'message' => 'Insufficient balance'];
    }
    
    if ($amount < 10) {
        return ['success' => false, 'message' => 'Minimum withdrawal amount is $10'];
    }
    
    if (createTransaction($user_id, 'withdrawal', $amount, null, $wallet_address)) {
        return ['success' => true, 'message' => 'Withdrawal request submitted successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to submit withdrawal request'];
    }
}

// Process deposit request
function processDepositRequest($user_id, $amount, $transaction_hash) {
    if ($amount < 10) {
        return ['success' => false, 'message' => 'Minimum deposit amount is $10'];
    }
    
    if (empty($transaction_hash)) {
        return ['success' => false, 'message' => 'Transaction hash is required'];
    }
    
    if (createTransaction($user_id, 'deposit', $amount, $transaction_hash)) {
        return ['success' => true, 'message' => 'Deposit request submitted successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to submit deposit request'];
    }
}
?>
