    </main>
    
    <!-- Footer -->
    <footer class="bg-primary text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-10 h-10 bg-light-blue rounded-lg flex items-center justify-center">
                            <i class="fas fa-anchor text-white text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">Poseidon Rental Pro</span>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        Invest in luxury real estate and yachts with confidence. Our platform provides secure, 
                        profitable investment opportunities in premium assets worldwide.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-light-blue transition-colors">
                            <i class="fab fa-facebook-f text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-light-blue transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-light-blue transition-colors">
                            <i class="fab fa-linkedin-in text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-light-blue transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="<?php echo getRelativePath('index.php'); ?>" class="text-gray-300 hover:text-light-blue transition-colors">Home</a></li>
                        <li><a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="text-gray-300 hover:text-light-blue transition-colors">Investments</a></li>
                        <li><a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="text-gray-300 hover:text-light-blue transition-colors">Blog</a></li>
                        <li><a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="text-gray-300 hover:text-light-blue transition-colors">Contact</a></li>
                        <?php if (isLoggedIn()): ?>
                            <li><a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="text-gray-300 hover:text-light-blue transition-colors">Dashboard</a></li>
                        <?php else: ?>
                            <li><a href="<?php echo getRelativePath('pages/login.php'); ?>" class="text-gray-300 hover:text-light-blue transition-colors">Login</a></li>
                            <li><a href="<?php echo getRelativePath('pages/register.php'); ?>" class="text-gray-300 hover:text-light-blue transition-colors">Register</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-phone"></i>
                            <span>+****************</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Miami, Florida, USA</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-clock"></i>
                            <span>24/7 Support</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Newsletter Signup -->
            <div class="border-t border-gray-600 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="mb-4 md:mb-0">
                        <h3 class="text-lg font-semibold mb-2">Stay Updated</h3>
                        <p class="text-gray-300">Subscribe to our newsletter for market insights and investment opportunities.</p>
                    </div>
                    <form class="flex w-full md:w-auto" onsubmit="subscribeNewsletter(event)">
                        <input 
                            type="email" 
                            placeholder="Enter your email" 
                            class="px-4 py-2 rounded-l-lg text-gray-900 flex-1 md:w-64 focus:outline-none focus:ring-2 focus:ring-light-blue"
                            required
                        >
                        <button 
                            type="submit" 
                            class="bg-light-blue hover:bg-blue-600 px-6 py-2 rounded-r-lg transition-colors"
                        >
                            Subscribe
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Copyright -->
            <div class="border-t border-gray-600 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; <?php echo date('Y'); ?> Poseidon Rental Pro. All rights reserved.</p>
                <div class="mt-2 space-x-4">
                    <a href="#" class="hover:text-light-blue transition-colors">Privacy Policy</a>
                    <a href="#" class="hover:text-light-blue transition-colors">Terms of Service</a>
                    <a href="#" class="hover:text-light-blue transition-colors">FAQ</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script>
        // Newsletter subscription
        function subscribeNewsletter(event) {
            event.preventDefault();
            const email = event.target.querySelector('input[type="email"]').value;
            
            // Here you would typically send the email to your backend
            alert('Thank you for subscribing! We\'ll keep you updated with the latest investment opportunities.');
            event.target.reset();
        }
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Auto-hide flash messages
        setTimeout(() => {
            const alerts = document.querySelectorAll('[role="alert"]');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            });
        }, 5000);
        
        // Add loading states to forms
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                }
            });
        });
        
        // Format currency inputs
        document.querySelectorAll('input[type="number"][step="0.01"]').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value) {
                    this.value = parseFloat(this.value).toFixed(2);
                }
            });
        });
        
        // Copy to clipboard functionality
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'fixed top-20 right-4 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
                toast.textContent = 'Copied to clipboard!';
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            });
        }
        
        // Confirmation dialogs for dangerous actions
        document.querySelectorAll('[data-confirm]').forEach(element => {
            element.addEventListener('click', function(e) {
                const message = this.getAttribute('data-confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
