    </main>
    
    <!-- Footer -->
    <footer class="bg-gradient-to-br from-dark-blue to-primary text-white relative overflow-hidden">
        <!-- Background decoration -->
        <div class="absolute inset-0 hero-pattern opacity-10"></div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-14 h-14 gradient-accent rounded-2xl flex items-center justify-center">
                            <i class="fas fa-anchor text-white text-2xl"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-white">Poseidon</div>
                            <div class="text-sm text-gray-300 -mt-1">Rental Pro</div>
                        </div>
                    </div>
                    <p class="text-gray-200 mb-8 max-w-md text-lg leading-relaxed">
                        The world's most trusted platform for luxury investment opportunities.
                        Join thousands of smart investors building wealth through premium assets.
                    </p>

                    <!-- Social Media -->
                    <div class="flex space-x-4 mb-8">
                        <a href="#" class="group w-12 h-12 bg-white bg-opacity-10 rounded-xl flex items-center justify-center hover:bg-opacity-20 transition-all duration-300 transform hover:scale-110">
                            <i class="fab fa-facebook-f text-xl group-hover:text-blue-300"></i>
                        </a>
                        <a href="#" class="group w-12 h-12 bg-white bg-opacity-10 rounded-xl flex items-center justify-center hover:bg-opacity-20 transition-all duration-300 transform hover:scale-110">
                            <i class="fab fa-twitter text-xl group-hover:text-blue-300"></i>
                        </a>
                        <a href="#" class="group w-12 h-12 bg-white bg-opacity-10 rounded-xl flex items-center justify-center hover:bg-opacity-20 transition-all duration-300 transform hover:scale-110">
                            <i class="fab fa-linkedin-in text-xl group-hover:text-blue-300"></i>
                        </a>
                        <a href="#" class="group w-12 h-12 bg-white bg-opacity-10 rounded-xl flex items-center justify-center hover:bg-opacity-20 transition-all duration-300 transform hover:scale-110">
                            <i class="fab fa-instagram text-xl group-hover:text-blue-300"></i>
                        </a>
                        <a href="#" class="group w-12 h-12 bg-white bg-opacity-10 rounded-xl flex items-center justify-center hover:bg-opacity-20 transition-all duration-300 transform hover:scale-110">
                            <i class="fab fa-youtube text-xl group-hover:text-blue-300"></i>
                        </a>
                    </div>

                    <!-- Trust badges -->
                    <div class="flex items-center space-x-4 text-sm text-gray-300">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt mr-2"></i>
                            <span>SEC Registered</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-lock mr-2"></i>
                            <span>Bank-Level Security</span>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-6 text-white">Platform</h3>
                    <ul class="space-y-3">
                        <li><a href="<?php echo getRelativePath('index.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="fas fa-home mr-3 w-4"></i>
                            <span>Home</span>
                            <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity"></i>
                        </a></li>
                        <li><a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="fas fa-chart-line mr-3 w-4"></i>
                            <span>Investments</span>
                            <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity"></i>
                        </a></li>
                        <li><a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="fas fa-newspaper mr-3 w-4"></i>
                            <span>Market Insights</span>
                            <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity"></i>
                        </a></li>
                        <li><a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="fas fa-envelope mr-3 w-4"></i>
                            <span>Contact</span>
                            <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity"></i>
                        </a></li>
                        <?php if (isLoggedIn()): ?>
                            <li><a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group">
                                <i class="fas fa-tachometer-alt mr-3 w-4"></i>
                                <span>Dashboard</span>
                                <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity"></i>
                            </a></li>
                        <?php else: ?>
                            <li><a href="<?php echo getRelativePath('pages/login.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group">
                                <i class="fas fa-sign-in-alt mr-3 w-4"></i>
                                <span>Login</span>
                                <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity"></i>
                            </a></li>
                            <li><a href="<?php echo getRelativePath('pages/register.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group">
                                <i class="fas fa-user-plus mr-3 w-4"></i>
                                <span>Get Started</span>
                                <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity"></i>
                            </a></li>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-xl font-bold mb-6 text-white">Get in Touch</h3>
                    <ul class="space-y-4 text-gray-300">
                        <li class="flex items-center space-x-3 group">
                            <div class="w-10 h-10 bg-white bg-opacity-10 rounded-lg flex items-center justify-center group-hover:bg-opacity-20 transition-all">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">Email</div>
                                <div class="font-medium"><EMAIL></div>
                            </div>
                        </li>
                        <li class="flex items-center space-x-3 group">
                            <div class="w-10 h-10 bg-white bg-opacity-10 rounded-lg flex items-center justify-center group-hover:bg-opacity-20 transition-all">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">Phone</div>
                                <div class="font-medium">+****************</div>
                            </div>
                        </li>
                        <li class="flex items-center space-x-3 group">
                            <div class="w-10 h-10 bg-white bg-opacity-10 rounded-lg flex items-center justify-center group-hover:bg-opacity-20 transition-all">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-400">Location</div>
                                <div class="font-medium">Miami, FL</div>
                            </div>
                        </li>
                        <li class="flex items-center space-x-3 group">
                            <div class="w-10 h-10 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-green-400"></i>
                            </div>
                            <div>
                                <div class="text-sm text-green-300">Support</div>
                                <div class="font-medium text-green-200">24/7 Available</div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Newsletter Signup -->
            <div class="border-t border-white border-opacity-20 mt-12 pt-12">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-3xl p-8">
                    <div class="flex flex-col lg:flex-row justify-between items-center">
                        <div class="mb-6 lg:mb-0 text-center lg:text-left">
                            <h3 class="text-2xl font-bold mb-3 text-white">Stay Ahead of the Market</h3>
                            <p class="text-gray-300 text-lg max-w-md">
                                Get exclusive investment opportunities, market insights, and expert analysis delivered to your inbox.
                            </p>
                            <div class="flex items-center mt-4 text-sm text-gray-400">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>Weekly market reports • Exclusive deals • No spam</span>
                            </div>
                        </div>
                        <form class="flex flex-col sm:flex-row w-full lg:w-auto" onsubmit="subscribeNewsletter(event)">
                            <input
                                type="email"
                                placeholder="Enter your email address"
                                class="px-6 py-4 rounded-xl sm:rounded-r-none text-gray-900 flex-1 lg:w-80 focus:outline-none focus:ring-4 focus:ring-blue-300 input-focus mb-3 sm:mb-0"
                                required
                            >
                            <button
                                type="submit"
                                class="btn-secondary text-white px-8 py-4 rounded-xl sm:rounded-l-none font-bold whitespace-nowrap"
                            >
                                <i class="fas fa-paper-plane mr-2"></i>
                                Subscribe Free
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-white border-opacity-20 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-center md:text-left mb-4 md:mb-0">
                        <p class="text-gray-300">&copy; <?php echo date('Y'); ?> Poseidon Rental Pro. All rights reserved.</p>
                        <p class="text-sm text-gray-400 mt-1">Building wealth through luxury investments since 2019</p>
                    </div>
                    <div class="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">Terms of Service</a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">Risk Disclosure</a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">FAQ</a>
                    </div>
                </div>

                <!-- Additional compliance info -->
                <div class="mt-6 pt-6 border-t border-white border-opacity-10 text-center">
                    <p class="text-xs text-gray-400 max-w-4xl mx-auto leading-relaxed">
                        Investment involves risk. Past performance is not indicative of future results.
                        Poseidon Rental Pro is a registered investment advisor. All investments are subject to market risk and may lose value.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script>
        // Newsletter subscription
        function subscribeNewsletter(event) {
            event.preventDefault();
            const email = event.target.querySelector('input[type="email"]').value;
            
            // Here you would typically send the email to your backend
            alert('Thank you for subscribing! We\'ll keep you updated with the latest investment opportunities.');
            event.target.reset();
        }
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Auto-hide flash messages
        setTimeout(() => {
            const alerts = document.querySelectorAll('[role="alert"]');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            });
        }, 5000);
        
        // Add loading states to forms
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                }
            });
        });
        
        // Format currency inputs
        document.querySelectorAll('input[type="number"][step="0.01"]').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value) {
                    this.value = parseFloat(this.value).toFixed(2);
                }
            });
        });
        
        // Copy to clipboard functionality
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'fixed top-20 right-4 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
                toast.textContent = 'Copied to clipboard!';
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            });
        }
        
        // Confirmation dialogs for dangerous actions
        document.querySelectorAll('[data-confirm]').forEach(element => {
            element.addEventListener('click', function(e) {
                const message = this.getAttribute('data-confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
