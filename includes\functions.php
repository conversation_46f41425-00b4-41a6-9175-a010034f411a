<?php
require_once 'config/database.php';
require_once 'config/config.php';

// User functions
function getUserById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function getUserByEmail($email) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch();
}

function createUser($username, $email, $password) {
    $db = getDB();
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt = $db->prepare("INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)");
    return $stmt->execute([$username, $email, $password_hash]);
}

function updateUserBalance($user_id, $amount, $operation = 'add') {
    $db = getDB();
    
    if ($operation === 'add') {
        $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
    } else {
        $stmt = $db->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
    }
    
    return $stmt->execute([$amount, $user_id]);
}

// Investment functions
function getAllInvestments($active_only = true) {
    $db = getDB();
    $sql = "SELECT * FROM investments";
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

function getInvestmentById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM investments WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function createUserInvestment($user_id, $investment_id, $amount, $monthly_rate, $return_period_months) {
    $db = getDB();
    
    $start_date = date('Y-m-d');
    $end_date = date('Y-m-d', strtotime("+{$return_period_months} months"));
    
    $stmt = $db->prepare("
        INSERT INTO user_investments (user_id, investment_id, amount, monthly_rate, start_date, end_date) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    return $stmt->execute([$user_id, $investment_id, $amount, $monthly_rate, $start_date, $end_date]);
}

function getUserInvestments($user_id) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT ui.*, i.title, i.category, i.image_url 
        FROM user_investments ui 
        JOIN investments i ON ui.investment_id = i.id 
        WHERE ui.user_id = ? 
        ORDER BY ui.created_at DESC
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}

// Transaction functions
function createTransaction($user_id, $type, $amount, $transaction_hash = null, $wallet_address = null) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, transaction_hash, wallet_address) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    return $stmt->execute([$user_id, $type, $amount, $transaction_hash, $wallet_address]);
}

function getUserTransactions($user_id, $limit = 10) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT * FROM transactions 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$user_id, $limit]);
    return $stmt->fetchAll();
}

function getPendingTransactions($type = null) {
    $db = getDB();
    $sql = "
        SELECT t.*, u.username, u.email 
        FROM transactions t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.status = 'pending'
    ";
    
    if ($type) {
        $sql .= " AND t.type = ?";
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute([$type]);
    } else {
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute();
    }
    
    return $stmt->fetchAll();
}

function updateTransactionStatus($transaction_id, $status, $admin_notes = null) {
    $db = getDB();
    $stmt = $db->prepare("
        UPDATE transactions 
        SET status = ?, admin_notes = ?, processed_at = NOW() 
        WHERE id = ?
    ");
    
    return $stmt->execute([$status, $admin_notes, $transaction_id]);
}

// Daily profit calculation
function calculateDailyProfit($user_investment_id, $amount, $monthly_rate) {
    $daily_rate = $monthly_rate / 30;
    return ($amount * $daily_rate) / 100;
}

function addDailyProfit($user_investment_id, $profit_amount, $date = null) {
    $db = getDB();
    
    if (!$date) {
        $date = date('Y-m-d');
    }
    
    $stmt = $db->prepare("
        INSERT IGNORE INTO daily_profits (user_investment_id, profit_amount, profit_date) 
        VALUES (?, ?, ?)
    ");
    
    return $stmt->execute([$user_investment_id, $profit_amount, $date]);
}

// Blog functions
function getBlogPosts($published_only = true, $limit = null) {
    $db = getDB();
    $sql = "SELECT * FROM blog_posts";
    
    if ($published_only) {
        $sql .= " WHERE is_published = 1";
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

function getBlogPostBySlug($slug) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND is_published = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Contact functions
function saveContactMessage($name, $email, $subject, $message) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO contact_messages (name, email, subject, message) 
        VALUES (?, ?, ?, ?)
    ");
    
    return $stmt->execute([$name, $email, $subject, $message]);
}

// Statistics functions
function getDashboardStats($user_id) {
    $db = getDB();
    
    // Get user balance
    $user = getUserById($user_id);
    $balance = $user['balance'];
    
    // Get total invested amount
    $stmt = $db->prepare("SELECT SUM(amount) as total_invested FROM user_investments WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $total_invested = $stmt->fetch()['total_invested'] ?? 0;
    
    // Get total profit earned
    $stmt = $db->prepare("
        SELECT SUM(dp.profit_amount) as total_profit 
        FROM daily_profits dp 
        JOIN user_investments ui ON dp.user_investment_id = ui.id 
        WHERE ui.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $total_profit = $stmt->fetch()['total_profit'] ?? 0;
    
    // Get active investments count
    $stmt = $db->prepare("SELECT COUNT(*) as active_investments FROM user_investments WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $active_investments = $stmt->fetch()['active_investments'];
    
    return [
        'balance' => $balance,
        'total_invested' => $total_invested,
        'total_profit' => $total_profit,
        'active_investments' => $active_investments
    ];
}
?>
