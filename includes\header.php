<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Invest in luxury real estate and yachts with Poseidon Rental Pro'; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-blue: #1e40af;
            --secondary-blue: #3b82f6;
            --light-blue: #60a5fa;
            --accent-blue: #93c5fd;
            --dark-blue: #1e3a8a;
            --success-green: #10b981;
            --warning-orange: #f59e0b;
            --error-red: #ef4444;
            --neutral-gray: #6b7280;
            --light-gray: #f8fafc;
            --white: #ffffff;
            --black: #0f172a;
        }

        .bg-primary { background-color: var(--primary-blue); }
        .bg-secondary { background-color: var(--secondary-blue); }
        .bg-light-blue { background-color: var(--light-blue); }
        .bg-accent { background-color: var(--accent-blue); }
        .bg-dark-blue { background-color: var(--dark-blue); }
        .text-primary { color: var(--primary-blue); }
        .text-secondary { color: var(--secondary-blue); }
        .text-light-blue { color: var(--light-blue); }
        .text-dark-blue { color: var(--dark-blue); }
        .border-primary { border-color: var(--primary-blue); }

        .gradient-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, var(--light-blue) 100%);
        }

        .gradient-secondary {
            background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--light-blue) 100%);
        }

        .gradient-accent {
            background: linear-gradient(135deg, var(--light-blue) 0%, var(--accent-blue) 100%);
        }

        .hero-pattern {
            background-image:
                radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(147, 197, 253, 0.1) 0%, transparent 50%);
        }

        .card-shadow {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            min-height: 48px;
            line-height: 1.2;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--light-blue) 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            min-height: 48px;
            line-height: 1.2;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(96, 165, 250, 0.4);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 10px 22px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            min-height: 48px;
            line-height: 1.2;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .navbar-glass {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        .text-gradient {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        /* Ensure text visibility on light backgrounds */
        .text-dark-contrast {
            color: var(--black) !important;
        }

        .text-medium-contrast {
            color: #374151 !important;
        }

        .text-light-contrast {
            color: #6b7280 !important;
        }

        /* Trust indicators visibility fix */
        .trust-indicators {
            color: #4b5563 !important;
        }

        /* Card text improvements */
        .card-text-primary {
            color: #1f2937 !important;
        }

        .card-text-secondary {
            color: #4b5563 !important;
        }

        /* Button improvements */
        .btn-outline.text-white {
            border-color: rgba(255, 255, 255, 0.8);
        }

        .btn-outline.text-white:hover {
            border-color: white;
            background-color: white;
            color: var(--primary-blue) !important;
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .animate-pulse-slow {
            animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .input-focus {
            transition: all 0.3s ease;
        }

        .input-focus:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .hero-pattern {
                background-size: 50px 50px;
            }

            .animate-float {
                animation-duration: 4s;
            }

            .text-5xl {
                font-size: 2.5rem;
            }

            .text-7xl {
                font-size: 3rem;
            }
        }

        /* Scroll animations */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }

        .fade-in-up.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-blue);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-blue);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-blue);
        }

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Focus styles for accessibility */
        *:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        button:focus,
        a:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }
    </style>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Mobile Styles -->
    <link rel="stylesheet" href="<?php echo getRelativePath('assets/css/mobile.css'); ?>">

    <style>
        body { font-family: 'Montserrat', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="navbar-glass fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="<?php echo getRelativePath('index.php'); ?>" class="flex items-center space-x-3 group">
                        <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center transform group-hover:scale-110 transition-all duration-300">
                            <i class="fas fa-anchor text-white text-xl"></i>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xl font-bold text-gradient">Poseidon</span>
                            <span class="text-sm text-neutral-gray -mt-1">Rental Pro</span>
                        </div>
                    </a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-1">
                    <a href="<?php echo getRelativePath('index.php'); ?>" class="px-4 py-2 rounded-lg text-gray-700 hover:text-primary hover:bg-blue-50 transition-all duration-300 font-medium">Home</a>
                    <a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="px-4 py-2 rounded-lg text-gray-700 hover:text-primary hover:bg-blue-50 transition-all duration-300 font-medium">Investments</a>
                    <a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="px-4 py-2 rounded-lg text-gray-700 hover:text-primary hover:bg-blue-50 transition-all duration-300 font-medium">Blog</a>
                    <a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="px-4 py-2 rounded-lg text-gray-700 hover:text-primary hover:bg-blue-50 transition-all duration-300 font-medium">Contact</a>
                    
                    <?php if (isLoggedIn()): ?>
                        <div class="flex items-center space-x-4">
                            <div class="relative group">
                                <button class="flex items-center space-x-2 px-4 py-2 rounded-lg text-gray-700 hover:bg-blue-50 transition-all duration-300">
                                    <div class="w-8 h-8 gradient-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                    <span class="font-medium"><?php echo $_SESSION['username']; ?></span>
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </button>
                                <div class="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform group-hover:translate-y-1">
                                    <div class="p-2">
                                        <a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-blue-50 rounded-lg transition-colors">
                                            <i class="fas fa-tachometer-alt text-primary"></i>
                                            <span>Dashboard</span>
                                        </a>
                                        <?php if (isAdmin()): ?>
                                            <a href="<?php echo getRelativePath('admin/index.php'); ?>" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-blue-50 rounded-lg transition-colors">
                                                <i class="fas fa-cog text-primary"></i>
                                                <span>Admin Panel</span>
                                            </a>
                                        <?php endif; ?>
                                        <hr class="my-2 border-gray-100">
                                        <a href="<?php echo getRelativePath('pages/logout.php'); ?>" class="flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <span>Logout</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center space-x-3">
                            <a href="<?php echo getRelativePath('pages/login.php'); ?>" class="px-4 py-2 text-gray-700 hover:text-primary transition-colors font-medium">Login</a>
                            <a href="<?php echo getRelativePath('pages/register.php'); ?>" class="btn-primary text-white px-6 py-2 rounded-lg font-medium">Get Started</a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="w-10 h-10 flex items-center justify-center text-gray-700 hover:text-primary hover:bg-blue-50 rounded-lg transition-all duration-300">
                        <i id="menu-icon" class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t border-gray-100 shadow-lg">
            <div class="px-6 py-6 space-y-1">
                <a href="<?php echo getRelativePath('index.php'); ?>" class="flex items-center space-x-3 py-3 px-4 text-gray-700 hover:text-primary hover:bg-blue-50 rounded-xl transition-all duration-300">
                    <i class="fas fa-home w-5"></i>
                    <span class="font-medium">Home</span>
                </a>
                <a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="flex items-center space-x-3 py-3 px-4 text-gray-700 hover:text-primary hover:bg-blue-50 rounded-xl transition-all duration-300">
                    <i class="fas fa-chart-line w-5"></i>
                    <span class="font-medium">Investments</span>
                </a>
                <a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="flex items-center space-x-3 py-3 px-4 text-gray-700 hover:text-primary hover:bg-blue-50 rounded-xl transition-all duration-300">
                    <i class="fas fa-newspaper w-5"></i>
                    <span class="font-medium">Blog</span>
                </a>
                <a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="flex items-center space-x-3 py-3 px-4 text-gray-700 hover:text-primary hover:bg-blue-50 rounded-xl transition-all duration-300">
                    <i class="fas fa-envelope w-5"></i>
                    <span class="font-medium">Contact</span>
                </a>

                <?php if (isLoggedIn()): ?>
                    <hr class="my-4 border-gray-200">
                    <div class="bg-blue-50 rounded-xl p-4 mb-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-10 h-10 gradient-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <div class="font-bold text-gray-900"><?php echo $_SESSION['username']; ?></div>
                                <div class="text-sm text-gray-600">Welcome back!</div>
                            </div>
                        </div>
                    </div>
                    <a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="flex items-center space-x-3 py-3 px-4 text-gray-700 hover:text-primary hover:bg-blue-50 rounded-xl transition-all duration-300">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <?php if (isAdmin()): ?>
                        <a href="<?php echo getRelativePath('admin/index.php'); ?>" class="flex items-center space-x-3 py-3 px-4 text-gray-700 hover:text-primary hover:bg-blue-50 rounded-xl transition-all duration-300">
                            <i class="fas fa-cog w-5"></i>
                            <span class="font-medium">Admin Panel</span>
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo getRelativePath('pages/logout.php'); ?>" class="flex items-center space-x-3 py-3 px-4 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl transition-all duration-300">
                        <i class="fas fa-sign-out-alt w-5"></i>
                        <span class="font-medium">Logout</span>
                    </a>
                <?php else: ?>
                    <hr class="my-4 border-gray-200">
                    <a href="<?php echo getRelativePath('pages/login.php'); ?>" class="flex items-center space-x-3 py-3 px-4 text-gray-700 hover:text-primary hover:bg-blue-50 rounded-xl transition-all duration-300">
                        <i class="fas fa-sign-in-alt w-5"></i>
                        <span class="font-medium">Login</span>
                    </a>
                    <a href="<?php echo getRelativePath('pages/register.php'); ?>" class="block mt-4 btn-primary text-white px-6 py-3 rounded-xl text-center font-bold">
                        <i class="fas fa-rocket mr-2"></i>
                        Get Started Free
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-16">
        
        <!-- Flash Messages -->
        <?php
        $success_message = getFlashMessage('success');
        $error_message = getFlashMessage('error');
        $info_message = getFlashMessage('info');
        ?>
        
        <?php if ($success_message): ?>
            <div class="fixed top-24 right-4 z-50 bg-green-500 text-white px-6 py-4 rounded-xl shadow-2xl border-l-4 border-green-600 max-w-md" role="alert">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-check text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">Success!</div>
                        <div class="text-sm opacity-90"><?php echo $success_message; ?></div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-green-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="fixed top-24 right-4 z-50 bg-red-500 text-white px-6 py-4 rounded-xl shadow-2xl border-l-4 border-red-600 max-w-md" role="alert">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-exclamation-triangle text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">Error!</div>
                        <div class="text-sm opacity-90"><?php echo $error_message; ?></div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-red-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($info_message): ?>
            <div class="fixed top-24 right-4 z-50 bg-blue-500 text-white px-6 py-4 rounded-xl shadow-2xl border-l-4 border-blue-600 max-w-md" role="alert">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-info text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">Info</div>
                        <div class="text-sm opacity-90"><?php echo $info_message; ?></div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-blue-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

    <script>
        // Mobile menu toggle with animation
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');

            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                menuIcon.classList.remove('fa-bars');
                menuIcon.classList.add('fa-times');
                // Add slide down animation
                mobileMenu.style.maxHeight = '0';
                mobileMenu.style.overflow = 'hidden';
                mobileMenu.style.transition = 'max-height 0.3s ease-out';
                setTimeout(() => {
                    mobileMenu.style.maxHeight = '500px';
                }, 10);
            } else {
                mobileMenu.style.maxHeight = '0';
                menuIcon.classList.remove('fa-times');
                menuIcon.classList.add('fa-bars');
                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                    mobileMenu.style.maxHeight = '';
                    mobileMenu.style.overflow = '';
                    mobileMenu.style.transition = '';
                }, 300);
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');

            if (!mobileMenu.contains(event.target) && !mobileMenuBtn.contains(event.target)) {
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenuBtn.click();
                }
            }
        });

        // Close mobile menu on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768) {
                const mobileMenu = document.getElementById('mobile-menu');
                const menuIcon = document.getElementById('menu-icon');
                mobileMenu.classList.add('hidden');
                menuIcon.classList.remove('fa-times');
                menuIcon.classList.add('fa-bars');
            }
        });

        // Scroll animations
        function initScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            // Add fade-in-up class to elements that should animate
            document.querySelectorAll('.card-shadow, .group').forEach(el => {
                el.classList.add('fade-in-up');
                observer.observe(el);
            });
        }

        // Navbar scroll effect
        function initNavbarScroll() {
            const navbar = document.querySelector('nav');
            let lastScrollY = window.scrollY;

            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;

                if (currentScrollY > 100) {
                    navbar.classList.add('shadow-lg');
                    navbar.style.backdropFilter = 'blur(20px)';
                } else {
                    navbar.classList.remove('shadow-lg');
                    navbar.style.backdropFilter = 'blur(10px)';
                }

                // Hide/show navbar on scroll
                if (currentScrollY > lastScrollY && currentScrollY > 200) {
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    navbar.style.transform = 'translateY(0)';
                }

                lastScrollY = currentScrollY;
            });
        }

        // Smooth scroll for anchor links
        function initSmoothScroll() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Initialize all animations and effects
        document.addEventListener('DOMContentLoaded', function() {
            initScrollAnimations();
            initNavbarScroll();
            initSmoothScroll();
        });
    </script>
