<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Invest in luxury real estate and yachts with Poseidon Rental Pro'; ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-blue: #003366;
            --light-blue: #00BFFF;
            --black: #000000;
            --white: #FFFFFF;
        }
        
        .bg-primary { background-color: var(--primary-blue); }
        .bg-light-blue { background-color: var(--light-blue); }
        .text-primary { color: var(--primary-blue); }
        .text-light-blue { color: var(--light-blue); }
        .border-primary { border-color: var(--primary-blue); }
        
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--light-blue) 100%);
        }
        
        .hero-pattern {
            background-image: 
                radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0),
                radial-gradient(circle at 75px 75px, rgba(255,255,255,0.1) 2px, transparent 0);
            background-size: 100px 100px;
        }
        
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 51, 102, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--light-blue) 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 191, 255, 0.4);
        }
        
        .navbar-glass {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { font-family: 'Montserrat', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="navbar-glass fixed w-full top-0 z-50 border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="<?php echo getRelativePath('index.php'); ?>" class="flex items-center space-x-2">
                        <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center">
                            <i class="fas fa-anchor text-white text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-primary">Poseidon Rental Pro</span>
                    </a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="<?php echo getRelativePath('index.php'); ?>" class="text-gray-700 hover:text-primary transition-colors">Home</a>
                    <a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="text-gray-700 hover:text-primary transition-colors">Investments</a>
                    <a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="text-gray-700 hover:text-primary transition-colors">Blog</a>
                    <a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="text-gray-700 hover:text-primary transition-colors">Contact</a>
                    
                    <?php if (isLoggedIn()): ?>
                        <div class="relative group">
                            <button class="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors">
                                <i class="fas fa-user"></i>
                                <span><?php echo $_SESSION['username']; ?></span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                <a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Dashboard</a>
                                <?php if (isAdmin()): ?>
                                    <a href="<?php echo getRelativePath('admin/index.php'); ?>" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Admin Panel</a>
                                <?php endif; ?>
                                <a href="<?php echo getRelativePath('pages/logout.php'); ?>" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Logout</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo getRelativePath('pages/login.php'); ?>" class="text-gray-700 hover:text-primary transition-colors">Login</a>
                        <a href="<?php echo getRelativePath('pages/register.php'); ?>" class="btn-primary text-white px-4 py-2 rounded-lg">Get Started</a>
                    <?php endif; ?>
                </div>
                
                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t border-gray-200">
            <div class="px-4 py-2 space-y-2">
                <a href="<?php echo getRelativePath('index.php'); ?>" class="block py-2 text-gray-700 hover:text-primary">Home</a>
                <a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="block py-2 text-gray-700 hover:text-primary">Investments</a>
                <a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="block py-2 text-gray-700 hover:text-primary">Blog</a>
                <a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="block py-2 text-gray-700 hover:text-primary">Contact</a>

                <?php if (isLoggedIn()): ?>
                    <a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="block py-2 text-gray-700 hover:text-primary">Dashboard</a>
                    <?php if (isAdmin()): ?>
                        <a href="<?php echo getRelativePath('admin/index.php'); ?>" class="block py-2 text-gray-700 hover:text-primary">Admin Panel</a>
                    <?php endif; ?>
                    <a href="<?php echo getRelativePath('pages/logout.php'); ?>" class="block py-2 text-gray-700 hover:text-primary">Logout</a>
                <?php else: ?>
                    <a href="<?php echo getRelativePath('pages/login.php'); ?>" class="block py-2 text-gray-700 hover:text-primary">Login</a>
                    <a href="<?php echo getRelativePath('pages/register.php'); ?>" class="block py-2 btn-primary text-white px-4 rounded-lg text-center">Get Started</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-16">
        
        <!-- Flash Messages -->
        <?php
        $success_message = getFlashMessage('success');
        $error_message = getFlashMessage('error');
        $info_message = getFlashMessage('info');
        ?>
        
        <?php if ($success_message): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mx-4 mt-4" role="alert">
                <span class="block sm:inline"><?php echo $success_message; ?></span>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mx-4 mt-4" role="alert">
                <span class="block sm:inline"><?php echo $error_message; ?></span>
            </div>
        <?php endif; ?>
        
        <?php if ($info_message): ?>
            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mx-4 mt-4" role="alert">
                <span class="block sm:inline"><?php echo $info_message; ?></span>
            </div>
        <?php endif; ?>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
