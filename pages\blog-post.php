<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

$slug = $_GET['slug'] ?? '';

if (!$slug) {
    redirect('blog.php');
}

$post = getBlogPostBySlug($slug);

if (!$post) {
    setFlashMessage('error', 'Blog post not found');
    redirect('blog.php');
}

$page_title = $post['title'];
$page_description = $post['excerpt'];

// Get other recent posts
$other_posts = getBlogPosts(true, 3);
$other_posts = array_filter($other_posts, function($p) use ($post) {
    return $p['id'] !== $post['id'];
});

include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50">
    <!-- Article Header -->
    <article class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Breadcrumb -->
        <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
            <a href="../index.php" class="hover:text-primary">Home</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <a href="blog.php" class="hover:text-primary">Blog</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span class="text-gray-400"><?php echo htmlspecialchars($post['title']); ?></span>
        </nav>

        <!-- Article Content -->
        <div class="bg-white rounded-2xl card-shadow overflow-hidden">
            <!-- Featured Image -->
            <div class="relative h-64 md:h-96">
                <img src="<?php echo $post['image_url'] ?: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'; ?>" 
                     alt="<?php echo htmlspecialchars($post['title']); ?>" 
                     class="w-full h-full object-cover">
            </div>

            <!-- Article Body -->
            <div class="p-8 md:p-12">
                <!-- Meta Information -->
                <div class="flex items-center justify-between mb-6 text-sm text-gray-600">
                    <div class="flex items-center space-x-4">
                        <span class="flex items-center">
                            <i class="fas fa-calendar mr-2"></i>
                            <?php echo date('F j, Y', strtotime($post['created_at'])); ?>
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            5 min read
                        </span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span>Share:</span>
                        <a href="#" class="text-blue-600 hover:text-blue-800">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-blue-400 hover:text-blue-600">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-blue-700 hover:text-blue-900">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>

                <!-- Title -->
                <h1 class="text-3xl md:text-4xl font-bold text-primary mb-6 leading-tight">
                    <?php echo htmlspecialchars($post['title']); ?>
                </h1>

                <!-- Excerpt -->
                <div class="text-xl text-gray-600 mb-8 leading-relaxed">
                    <?php echo htmlspecialchars($post['excerpt']); ?>
                </div>

                <!-- Content -->
                <div class="prose prose-lg max-w-none">
                    <?php echo nl2br(htmlspecialchars($post['content'])); ?>
                </div>

                <!-- Tags/Categories -->
                <div class="mt-12 pt-8 border-t border-gray-200">
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm">Investment</span>
                        <span class="bg-light-blue text-white px-3 py-1 rounded-full text-sm">Market Analysis</span>
                        <span class="bg-gray-600 text-white px-3 py-1 rounded-full text-sm">Luxury Assets</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Author Bio -->
        <div class="mt-8 bg-white rounded-2xl card-shadow p-8">
            <div class="flex items-start space-x-4">
                <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-xl"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-primary mb-2">Investment Team</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Our expert investment team brings decades of experience in luxury real estate and maritime assets. 
                        We provide in-depth market analysis and insights to help our investors make informed decisions.
                    </p>
                </div>
            </div>
        </div>

        <!-- Related Posts -->
        <?php if (!empty($other_posts)): ?>
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-primary mb-8">Related Articles</h2>
            <div class="grid grid-cols-1 md:grid-cols-<?php echo min(count($other_posts), 3); ?> gap-8">
                <?php foreach (array_slice($other_posts, 0, 3) as $related_post): ?>
                    <article class="bg-white rounded-xl card-shadow hover:shadow-lg transition-shadow overflow-hidden">
                        <img src="<?php echo $related_post['image_url'] ?: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>" 
                             alt="<?php echo htmlspecialchars($related_post['title']); ?>" 
                             class="w-full h-48 object-cover">
                        <div class="p-6">
                            <h3 class="text-lg font-bold text-primary mb-2 hover:text-light-blue">
                                <a href="blog-post.php?slug=<?php echo $related_post['slug']; ?>">
                                    <?php echo htmlspecialchars($related_post['title']); ?>
                                </a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-4"><?php echo htmlspecialchars($related_post['excerpt']); ?></p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500"><?php echo date('M j, Y', strtotime($related_post['created_at'])); ?></span>
                                <a href="blog-post.php?slug=<?php echo $related_post['slug']; ?>" 
                                   class="text-primary hover:text-light-blue font-semibold text-sm">
                                    Read More →
                                </a>
                            </div>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Newsletter CTA -->
        <div class="mt-12 bg-gradient-to-r from-primary to-light-blue rounded-2xl p-8 text-white text-center">
            <h2 class="text-2xl font-bold mb-4">Stay Updated</h2>
            <p class="text-gray-200 mb-6">
                Subscribe to our newsletter for the latest market insights and investment opportunities
            </p>
            <form class="max-w-md mx-auto flex" onsubmit="subscribeNewsletter(event)">
                <input 
                    type="email" 
                    placeholder="Enter your email" 
                    class="flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none"
                    required
                >
                <button 
                    type="submit" 
                    class="bg-white text-primary px-6 py-3 rounded-r-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                    Subscribe
                </button>
            </form>
        </div>
    </article>
</div>

<script>
function subscribeNewsletter(event) {
    event.preventDefault();
    const email = event.target.querySelector('input[type="email"]').value;
    
    alert('Thank you for subscribing! We\'ll keep you updated with the latest insights.');
    event.target.reset();
}
</script>

<?php include '../includes/footer.php'; ?>
