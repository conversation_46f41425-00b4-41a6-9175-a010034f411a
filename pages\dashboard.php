<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

requireLogin();

$page_title = 'Dashboard';
$user_id = getCurrentUserId();

// Get dashboard statistics
$stats = getDashboardStats($user_id);

// Get user investments
$user_investments = getUserInvestments($user_id);

// Get recent transactions
$recent_transactions = getUserTransactions($user_id, 5);

// Handle deposit request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'deposit') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $amount = floatval($_POST['amount'] ?? 0);
        $transaction_hash = sanitizeInput($_POST['transaction_hash'] ?? '');
        
        $result = processDepositRequest($user_id, $amount, $transaction_hash);
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
        } else {
            setFlashMessage('error', $result['message']);
        }
        
        redirect('dashboard.php');
    }
}

// Handle withdrawal request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'withdrawal') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $amount = floatval($_POST['amount'] ?? 0);
        $wallet_address = sanitizeInput($_POST['wallet_address'] ?? '');
        
        $result = processWithdrawalRequest($user_id, $amount, $wallet_address);
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
        } else {
            setFlashMessage('error', $result['message']);
        }
        
        redirect('dashboard.php');
    }
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-primary">Welcome back, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h1>
            <p class="text-gray-600 mt-2">Manage your investments and track your portfolio performance</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-wallet text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Available Balance</p>
                        <p class="text-2xl font-bold text-primary"><?php echo formatCurrency($stats['balance']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Invested</p>
                        <p class="text-2xl font-bold text-primary"><?php echo formatCurrency($stats['total_invested']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-coins text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Profit</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo formatCurrency($stats['total_profit']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-briefcase text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Active Investments</p>
                        <p class="text-2xl font-bold text-primary"><?php echo $stats['active_investments']; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Active Investments -->
                <div class="bg-white rounded-xl card-shadow">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-bold text-primary">Active Investments</h2>
                            <a href="investments.php" class="text-light-blue hover:text-primary font-semibold">
                                Browse More →
                            </a>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php if (empty($user_investments)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-600 mb-4">You haven't made any investments yet</p>
                                <a href="investments.php" class="btn-primary text-white px-6 py-2 rounded-lg">
                                    Start Investing
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($user_investments as $investment): ?>
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div class="flex items-center space-x-4">
                                                <img src="<?php echo $investment['image_url'] ?: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'; ?>" 
                                                     alt="<?php echo htmlspecialchars($investment['title']); ?>" 
                                                     class="w-16 h-16 object-cover rounded-lg">
                                                <div>
                                                    <h3 class="font-semibold text-primary"><?php echo htmlspecialchars($investment['title']); ?></h3>
                                                    <p class="text-sm text-gray-600"><?php echo ucfirst(str_replace('_', ' ', $investment['category'])); ?></p>
                                                    <p class="text-sm text-gray-500">Started: <?php echo date('M j, Y', strtotime($investment['start_date'])); ?></p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="text-lg font-bold text-primary"><?php echo formatCurrency($investment['amount']); ?></p>
                                                <p class="text-sm text-green-600"><?php echo formatPercentage($investment['monthly_rate']); ?> monthly</p>
                                                <p class="text-sm text-gray-500">Profit: <?php echo formatCurrency($investment['total_profit']); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white rounded-xl card-shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-primary">Recent Transactions</h2>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_transactions)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-receipt text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-600">No transactions yet</p>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <div class="flex justify-between items-center py-3 border-b border-gray-100 last:border-b-0">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 rounded-full flex items-center justify-center <?php echo $transaction['type'] === 'deposit' ? 'bg-green-100' : 'bg-red-100'; ?>">
                                                <i class="fas <?php echo $transaction['type'] === 'deposit' ? 'fa-arrow-down text-green-600' : 'fa-arrow-up text-red-600'; ?>"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold"><?php echo ucfirst($transaction['type']); ?></p>
                                                <p class="text-sm text-gray-500"><?php echo timeAgo($transaction['created_at']); ?></p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold <?php echo $transaction['type'] === 'deposit' ? 'text-green-600' : 'text-red-600'; ?>">
                                                <?php echo $transaction['type'] === 'deposit' ? '+' : '-'; ?><?php echo formatCurrency($transaction['amount']); ?>
                                            </p>
                                            <span class="text-xs px-2 py-1 rounded-full <?php 
                                                echo $transaction['status'] === 'approved' ? 'bg-green-100 text-green-800' : 
                                                    ($transaction['status'] === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'); 
                                            ?>">
                                                <?php echo ucfirst($transaction['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-bold text-primary mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <button onclick="openDepositModal()" class="w-full btn-primary text-white py-3 rounded-lg font-semibold">
                            <i class="fas fa-plus mr-2"></i>Deposit Funds
                        </button>
                        <button onclick="openWithdrawalModal()" class="w-full border-2 border-primary text-primary py-3 rounded-lg font-semibold hover:bg-primary hover:text-white transition-colors">
                            <i class="fas fa-minus mr-2"></i>Withdraw Funds
                        </button>
                        <a href="investments.php" class="block w-full text-center border-2 border-gray-300 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                            <i class="fas fa-search mr-2"></i>Browse Investments
                        </a>
                    </div>
                </div>

                <!-- Account Info -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-bold text-primary mb-4">Account Information</h3>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-600">Username</p>
                            <p class="font-semibold"><?php echo htmlspecialchars($_SESSION['username']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Email</p>
                            <p class="font-semibold"><?php echo htmlspecialchars($_SESSION['email']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Member Since</p>
                            <p class="font-semibold"><?php echo date('M Y'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Support -->
                <div class="bg-gradient-to-r from-primary to-light-blue rounded-xl p-6 text-white">
                    <h3 class="text-lg font-bold mb-2">Need Help?</h3>
                    <p class="text-sm mb-4 text-gray-200">Our support team is available 24/7 to assist you</p>
                    <a href="contact.php" class="bg-white text-primary px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-100 transition-colors">
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deposit Modal -->
<div id="depositModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-primary">Deposit Funds</h3>
                <button onclick="closeDepositModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="deposit">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Amount (USDT)</label>
                    <input type="number" name="amount" step="0.01" min="10" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-blue">
                    <p class="text-sm text-gray-500 mt-1">Minimum deposit: $10</p>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Transaction Hash</label>
                    <input type="text" name="transaction_hash" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-blue"
                           placeholder="Enter USDT transaction hash">
                </div>
                
                <div class="bg-blue-50 p-4 rounded-lg mb-4">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-info-circle mr-2"></i>
                        Send USDT to our wallet and paste the transaction hash above. Your deposit will be processed within 24 hours.
                    </p>
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" onclick="closeDepositModal()" 
                            class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 btn-primary text-white py-2 rounded-lg">
                        Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Withdrawal Modal -->
<div id="withdrawalModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-primary">Withdraw Funds</h3>
                <button onclick="closeWithdrawalModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="withdrawal">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Amount (USDT)</label>
                    <input type="number" name="amount" step="0.01" min="10" max="<?php echo $stats['balance']; ?>" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-blue">
                    <p class="text-sm text-gray-500 mt-1">Available: <?php echo formatCurrency($stats['balance']); ?></p>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">USDT Wallet Address</label>
                    <input type="text" name="wallet_address" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-blue"
                           placeholder="Enter your USDT wallet address">
                </div>
                
                <div class="bg-yellow-50 p-4 rounded-lg mb-4">
                    <p class="text-sm text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Withdrawal requests are processed manually within 24-48 hours. Please ensure your wallet address is correct.
                    </p>
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" onclick="closeWithdrawalModal()" 
                            class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 btn-primary text-white py-2 rounded-lg">
                        Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openDepositModal() {
    document.getElementById('depositModal').classList.remove('hidden');
}

function closeDepositModal() {
    document.getElementById('depositModal').classList.add('hidden');
}

function openWithdrawalModal() {
    document.getElementById('withdrawalModal').classList.remove('hidden');
}

function closeWithdrawalModal() {
    document.getElementById('withdrawalModal').classList.add('hidden');
}

// Close modals when clicking outside
document.getElementById('depositModal').addEventListener('click', function(e) {
    if (e.target === this) closeDepositModal();
});

document.getElementById('withdrawalModal').addEventListener('click', function(e) {
    if (e.target === this) closeWithdrawalModal();
});
</script>

<?php include '../includes/footer.php'; ?>
