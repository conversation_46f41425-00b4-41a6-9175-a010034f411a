<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

$investment_id = intval($_GET['id'] ?? 0);

if (!$investment_id) {
    redirect('investments.php');
}

$investment = getInvestmentById($investment_id);

if (!$investment || !$investment['is_active']) {
    setFlashMessage('error', 'Investment not found or no longer available');
    redirect('investments.php');
}

$page_title = $investment['title'];
$page_description = $investment['description'];

// Get similar investments
$similar_investments = getAllInvestments(true);
$similar_investments = array_filter($similar_investments, function($inv) use ($investment_id, $investment) {
    return $inv['id'] != $investment_id && $inv['category'] === $investment['category'];
});
$similar_investments = array_slice($similar_investments, 0, 3);

include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50">
    <!-- Investment Hero -->
    <section class="relative">
        <div class="h-96 lg:h-[500px] relative overflow-hidden">
            <img src="<?php echo $investment['image_url'] ?: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'; ?>" 
                 alt="<?php echo htmlspecialchars($investment['title']); ?>" 
                 class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-black bg-opacity-40"></div>
            
            <!-- Breadcrumb -->
            <div class="absolute top-4 left-4 z-10">
                <nav class="flex items-center space-x-2 text-white text-sm">
                    <a href="../index.php" class="hover:text-light-blue">Home</a>
                    <i class="fas fa-chevron-right text-xs"></i>
                    <a href="investments.php" class="hover:text-light-blue">Investments</a>
                    <i class="fas fa-chevron-right text-xs"></i>
                    <span class="text-gray-300"><?php echo htmlspecialchars($investment['title']); ?></span>
                </nav>
            </div>

            <!-- Investment Info Overlay -->
            <div class="absolute bottom-0 left-0 right-0 p-6 lg:p-8 text-white">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-end">
                        <div>
                            <div class="flex items-center space-x-3 mb-4">
                                <span class="bg-primary px-3 py-1 rounded-full text-sm font-semibold">
                                    <?php echo ucfirst(str_replace('_', ' ', $investment['category'])); ?>
                                </span>
                                <?php if ($investment['capital_return']): ?>
                                    <span class="bg-green-500 px-3 py-1 rounded-full text-sm font-semibold">
                                        Capital Return
                                    </span>
                                <?php endif; ?>
                            </div>
                            <h1 class="text-3xl lg:text-5xl font-bold mb-2"><?php echo htmlspecialchars($investment['title']); ?></h1>
                            <p class="text-xl text-gray-200 flex items-center">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                <?php echo htmlspecialchars($investment['location']); ?>
                            </p>
                        </div>
                        <div class="mt-6 lg:mt-0 text-right">
                            <p class="text-sm text-gray-300 mb-1">Investment Amount</p>
                            <p class="text-4xl font-bold"><?php echo formatCurrency($investment['price']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Investment Overview -->
                <div class="bg-white rounded-2xl card-shadow p-8 mb-8">
                    <h2 class="text-2xl font-bold text-primary mb-6">Investment Overview</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div class="bg-gray-50 p-6 rounded-xl">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-percentage text-green-600 text-xl mr-3"></i>
                                <h3 class="font-semibold text-gray-800">Monthly Returns</h3>
                            </div>
                            <p class="text-2xl font-bold text-green-600">
                                <?php echo formatPercentage($investment['monthly_rate_min']); ?> - <?php echo formatPercentage($investment['monthly_rate_max']); ?>
                            </p>
                            <p class="text-sm text-gray-600 mt-1">Calculated daily and paid monthly</p>
                        </div>

                        <div class="bg-gray-50 p-6 rounded-xl">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-calendar-alt text-blue-600 text-xl mr-3"></i>
                                <h3 class="font-semibold text-gray-800">Investment Period</h3>
                            </div>
                            <p class="text-2xl font-bold text-primary"><?php echo $investment['return_period_months']; ?> Months</p>
                            <p class="text-sm text-gray-600 mt-1">
                                <?php echo $investment['capital_return'] ? 'Capital returned at end' : 'Capital not returned'; ?>
                            </p>
                        </div>
                    </div>

                    <?php if ($investment['description']): ?>
                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-primary mb-4">Description</h3>
                            <p class="text-gray-700 leading-relaxed"><?php echo nl2br(htmlspecialchars($investment['description'])); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if ($investment['features']): ?>
                        <div>
                            <h3 class="text-xl font-bold text-primary mb-4">Key Features</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <?php 
                                $features = explode(',', $investment['features']);
                                foreach ($features as $feature): 
                                ?>
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span class="text-gray-700"><?php echo trim(htmlspecialchars($feature)); ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Investment Calculator -->
                <div class="bg-white rounded-2xl card-shadow p-8 mb-8">
                    <h2 class="text-2xl font-bold text-primary mb-6">Investment Calculator</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Investment Amount</label>
                            <input type="number" id="calc_amount" min="<?php echo MIN_INVESTMENT_AMOUNT; ?>" max="<?php echo $investment['price']; ?>" 
                                   value="<?php echo MIN_INVESTMENT_AMOUNT; ?>" step="100"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-blue"
                                   onchange="calculateReturns()">
                            <p class="text-sm text-gray-500 mt-1">Min: <?php echo formatCurrency(MIN_INVESTMENT_AMOUNT); ?> | Max: <?php echo formatCurrency($investment['price']); ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Expected Monthly Rate</label>
                            <input type="range" id="calc_rate" 
                                   min="<?php echo $investment['monthly_rate_min']; ?>" 
                                   max="<?php echo $investment['monthly_rate_max']; ?>" 
                                   value="<?php echo ($investment['monthly_rate_min'] + $investment['monthly_rate_max']) / 2; ?>" 
                                   step="0.1" class="w-full" onchange="calculateReturns()">
                            <div class="flex justify-between text-sm text-gray-500 mt-1">
                                <span><?php echo formatPercentage($investment['monthly_rate_min']); ?></span>
                                <span id="current_rate"><?php echo formatPercentage(($investment['monthly_rate_min'] + $investment['monthly_rate_max']) / 2); ?></span>
                                <span><?php echo formatPercentage($investment['monthly_rate_max']); ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-blue-50 p-6 rounded-xl text-center">
                            <h4 class="font-semibold text-gray-800 mb-2">Daily Profit</h4>
                            <p class="text-2xl font-bold text-blue-600" id="daily_profit">$0.00</p>
                        </div>
                        
                        <div class="bg-green-50 p-6 rounded-xl text-center">
                            <h4 class="font-semibold text-gray-800 mb-2">Monthly Profit</h4>
                            <p class="text-2xl font-bold text-green-600" id="monthly_profit">$0.00</p>
                        </div>
                        
                        <div class="bg-purple-50 p-6 rounded-xl text-center">
                            <h4 class="font-semibold text-gray-800 mb-2">Total Return</h4>
                            <p class="text-2xl font-bold text-purple-600" id="total_return">$0.00</p>
                        </div>
                    </div>
                </div>

                <!-- Risk Information -->
                <div class="bg-white rounded-2xl card-shadow p-8">
                    <h2 class="text-2xl font-bold text-primary mb-6">Risk Information</h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-shield-alt text-green-500 text-xl mr-4 mt-1"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-1">Asset-Backed Security</h4>
                                <p class="text-gray-600">All investments are backed by real, tangible assets with comprehensive insurance coverage.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <i class="fas fa-chart-line text-blue-500 text-xl mr-4 mt-1"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-1">Market Performance</h4>
                                <p class="text-gray-600">Returns may vary based on market conditions and asset performance.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <i class="fas fa-users text-purple-500 text-xl mr-4 mt-1"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-1">Professional Management</h4>
                                <p class="text-gray-600">Experienced team manages all aspects of the investment to maximize returns.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Investment Action -->
                <div class="bg-white rounded-2xl card-shadow p-6 sticky top-24">
                    <div class="text-center mb-6">
                        <h3 class="text-2xl font-bold text-primary mb-2">Ready to Invest?</h3>
                        <p class="text-gray-600">Start generating passive income today</p>
                    </div>

                    <?php if (isLoggedIn()): ?>
                        <button onclick="openInvestModal(<?php echo $investment['id']; ?>, '<?php echo htmlspecialchars($investment['title']); ?>', <?php echo $investment['price']; ?>)" 
                                class="w-full btn-primary text-white py-4 rounded-lg font-semibold text-lg mb-4">
                            <i class="fas fa-chart-line mr-2"></i>Invest Now
                        </button>
                        
                        <div class="text-center text-sm text-gray-600">
                            <p>Available Balance: <?php echo formatCurrency(getUserById(getCurrentUserId())['balance']); ?></p>
                        </div>
                    <?php else: ?>
                        <a href="login.php" class="block w-full btn-primary text-white text-center py-4 rounded-lg font-semibold text-lg mb-4">
                            <i class="fas fa-sign-in-alt mr-2"></i>Login to Invest
                        </a>
                        
                        <a href="register.php" class="block w-full border-2 border-primary text-primary text-center py-3 rounded-lg font-semibold hover:bg-primary hover:text-white transition-colors">
                            Create Account
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Investment Summary -->
                <div class="bg-white rounded-2xl card-shadow p-6">
                    <h3 class="text-lg font-bold text-primary mb-4">Investment Summary</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Category</span>
                            <span class="font-semibold"><?php echo ucfirst(str_replace('_', ' ', $investment['category'])); ?></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Investment Amount</span>
                            <span class="font-semibold"><?php echo formatCurrency($investment['price']); ?></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Return Range</span>
                            <span class="font-semibold text-green-600">
                                <?php echo formatPercentage($investment['monthly_rate_min']); ?> - <?php echo formatPercentage($investment['monthly_rate_max']); ?>
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Duration</span>
                            <span class="font-semibold"><?php echo $investment['return_period_months']; ?> months</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Capital Return</span>
                            <span class="font-semibold <?php echo $investment['capital_return'] ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $investment['capital_return'] ? 'Yes' : 'No'; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Contact Support -->
                <div class="bg-gradient-to-r from-primary to-light-blue rounded-2xl p-6 text-white">
                    <h3 class="text-lg font-bold mb-2">Need Help?</h3>
                    <p class="text-sm text-gray-200 mb-4">Our investment experts are here to help you make informed decisions.</p>
                    <a href="contact.php" class="bg-white text-primary px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-100 transition-colors">
                        Contact Support
                    </a>
                </div>
            </div>
        </div>

        <!-- Similar Investments -->
        <?php if (!empty($similar_investments)): ?>
        <div class="mt-16">
            <h2 class="text-3xl font-bold text-primary text-center mb-8">Similar Investments</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php foreach ($similar_investments as $similar): ?>
                    <div class="bg-white rounded-2xl card-shadow hover:shadow-xl transition-shadow overflow-hidden">
                        <img src="<?php echo $similar['image_url'] ?: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>" 
                             alt="<?php echo htmlspecialchars($similar['title']); ?>" 
                             class="w-full h-48 object-cover">
                        <div class="p-6">
                            <h3 class="text-lg font-bold text-primary mb-2"><?php echo htmlspecialchars($similar['title']); ?></h3>
                            <p class="text-gray-600 mb-4"><?php echo htmlspecialchars($similar['location']); ?></p>
                            <div class="flex justify-between items-center mb-4">
                                <span class="text-xl font-bold text-primary"><?php echo formatCurrency($similar['price']); ?></span>
                                <span class="text-green-600 font-semibold">
                                    <?php echo formatPercentage($similar['monthly_rate_max']); ?>
                                </span>
                            </div>
                            <a href="investment-detail.php?id=<?php echo $similar['id']; ?>" 
                               class="block w-full btn-primary text-white text-center py-2 rounded-lg font-semibold">
                                View Details
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php if (isLoggedIn()): ?>
<!-- Investment Modal -->
<div id="investModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-primary">Confirm Investment</h3>
                <button onclick="closeInvestModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div class="text-center mb-6">
                <h4 class="font-bold text-lg text-primary mb-2"><?php echo htmlspecialchars($investment['title']); ?></h4>
                <p class="text-gray-600">Maximum Investment: <?php echo formatCurrency($investment['price']); ?></p>
            </div>
            
            <form method="POST" action="process-investment.php">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="investment_id" value="<?php echo $investment['id']; ?>">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Investment Amount</label>
                    <input type="number" name="amount" step="0.01" min="<?php echo MIN_INVESTMENT_AMOUNT; ?>" max="<?php echo $investment['price']; ?>" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-blue">
                    <p class="text-sm text-gray-500 mt-1">
                        Available balance: <?php echo formatCurrency(getUserById(getCurrentUserId())['balance']); ?>
                    </p>
                </div>
                
                <div class="bg-blue-50 p-4 rounded-lg mb-4">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-info-circle mr-2"></i>
                        Your investment will start generating daily returns immediately after confirmation.
                    </p>
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" onclick="closeInvestModal()" 
                            class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 btn-primary text-white py-2 rounded-lg">
                        Confirm Investment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Investment calculator
function calculateReturns() {
    const amount = parseFloat(document.getElementById('calc_amount').value) || 0;
    const rate = parseFloat(document.getElementById('calc_rate').value) || 0;
    const period = <?php echo $investment['return_period_months']; ?>;
    
    const dailyRate = rate / 30;
    const dailyProfit = (amount * dailyRate) / 100;
    const monthlyProfit = (amount * rate) / 100;
    const totalProfit = monthlyProfit * period;
    
    document.getElementById('current_rate').textContent = rate.toFixed(1) + '%';
    document.getElementById('daily_profit').textContent = '$' + dailyProfit.toFixed(2);
    document.getElementById('monthly_profit').textContent = '$' + monthlyProfit.toFixed(2);
    document.getElementById('total_return').textContent = '$' + totalProfit.toFixed(2);
}

// Initialize calculator
calculateReturns();

<?php if (isLoggedIn()): ?>
function openInvestModal(investmentId, title, price) {
    document.getElementById('investModal').classList.remove('hidden');
}

function closeInvestModal() {
    document.getElementById('investModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('investModal').addEventListener('click', function(e) {
    if (e.target === this) closeInvestModal();
});
<?php endif; ?>
</script>

<?php include '../includes/footer.php'; ?>
